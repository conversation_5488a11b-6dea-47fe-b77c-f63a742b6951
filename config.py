#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理所有环境变量配置
"""

import os
from typing import Union
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_env_bool(key: str, default: bool = False) -> bool:
    """获取布尔类型的环境变量"""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

def get_env_int(key: str, default: int = 0) -> int:
    """获取整数类型的环境变量"""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default

def get_env_list(key: str, default: list = None, separator: str = ',') -> list:
    """获取列表类型的环境变量"""
    if default is None:
        default = []
    value = os.getenv(key, '')
    if not value:
        return default
    return [item.strip() for item in value.split(separator) if item.strip()]

class Config:
    """配置类 - 统一管理所有配置项"""
    
    # MySQL数据库配置
    MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT = get_env_int('MYSQL_PORT', 3306)
    MYSQL_USER = os.getenv('MYSQL_USER', 'root')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'bill_analysis_db')
    MYSQL_CHARSET = os.getenv('MYSQL_CHARSET', 'utf8mb4')
    
    # Flask应用配置
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = get_env_bool('FLASK_DEBUG', True)
    HOST = os.getenv('FLASK_HOST', '127.0.0.1')
    PORT = get_env_int('FLASK_PORT', 5000)
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = get_env_int('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
    JSON_AS_ASCII = get_env_bool('JSON_AS_ASCII', False)
    
    # 邮箱SMTP配置
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.qq.com')
    SMTP_PORT = get_env_int('SMTP_PORT', 465)
    SMTP_USERNAME = os.getenv('SMTP_USERNAME', '')
    SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
    EMAIL_USE_SSL = get_env_bool('EMAIL_USE_SSL', True)
    
    # 应用功能配置
    VERIFICATION_CODE_LENGTH = get_env_int('VERIFICATION_CODE_LENGTH', 6)
    VERIFICATION_CODE_EXPIRE_MINUTES = get_env_int('VERIFICATION_CODE_EXPIRE_MINUTES', 5)
    ALLOWED_EXTENSIONS = get_env_list('ALLOWED_EXTENSIONS', ['csv'])
    IMAGE_EXTENSIONS = get_env_list('IMAGE_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif'])
    MAX_IMAGE_SIZE = get_env_int('MAX_IMAGE_SIZE', 2 * 1024 * 1024)
    AVATAR_SIZE = get_env_int('AVATAR_SIZE', 200)
    
    # 安全配置
    PASSWORD_MIN_LENGTH = get_env_int('PASSWORD_MIN_LENGTH', 6)
    SESSION_PERMANENT = get_env_bool('SESSION_PERMANENT', False)
    SESSION_COOKIE_SECURE = get_env_bool('SESSION_COOKIE_SECURE', False)
    SESSION_COOKIE_HTTPONLY = get_env_bool('SESSION_COOKIE_HTTPONLY', True)
    
    # 文件处理配置
    CSV_SKIP_ROWS = get_env_int('CSV_SKIP_ROWS', 10)
    DEFAULT_ENCODING = os.getenv('DEFAULT_ENCODING', 'utf-8')
    BACKUP_ENABLED = get_env_bool('BACKUP_ENABLED', True)
    
    # 开发配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    ENABLE_CORS = get_env_bool('ENABLE_CORS', False)

    # 管理员配置
    ADMIN_REGISTRATION_ENABLED = get_env_bool('ADMIN_REGISTRATION_ENABLED', True)
    ADMIN_REGISTRATION_SECRET = os.getenv('ADMIN_REGISTRATION_SECRET', 'admin_secret_2025')
    ADMIN_SESSION_TIMEOUT = get_env_int('ADMIN_SESSION_TIMEOUT', 3600)  # 1小时

    # 系统监控配置
    SYSTEM_MONITORING_ENABLED = get_env_bool('SYSTEM_MONITORING_ENABLED', True)
    LOG_RETENTION_DAYS = get_env_int('LOG_RETENTION_DAYS', 30)
    
    @classmethod
    def get_mysql_config(cls) -> dict:
        """获取MySQL连接配置"""
        import pymysql.cursors
        return {
            'host': cls.MYSQL_HOST,
            'port': cls.MYSQL_PORT,
            'user': cls.MYSQL_USER,
            'password': cls.MYSQL_PASSWORD,
            'database': cls.MYSQL_DATABASE,
            'charset': cls.MYSQL_CHARSET,
            'cursorclass': pymysql.cursors.DictCursor,
            'autocommit': True
        }
    
    @classmethod
    def get_smtp_config(cls) -> dict:
        """获取SMTP配置"""
        return {
            'server': cls.SMTP_SERVER,
            'port': cls.SMTP_PORT,
            'username': cls.SMTP_USERNAME,
            'password': cls.SMTP_PASSWORD,
            'use_ssl': cls.EMAIL_USE_SSL
        }
    
    @classmethod
    def get_flask_config(cls) -> dict:
        """获取Flask配置"""
        return {
            'SECRET_KEY': cls.SECRET_KEY,
            'DEBUG': cls.DEBUG,
            'UPLOAD_FOLDER': os.path.abspath(cls.UPLOAD_FOLDER),
            'MAX_CONTENT_LENGTH': cls.MAX_CONTENT_LENGTH,
            'JSON_AS_ASCII': cls.JSON_AS_ASCII,
            'SESSION_PERMANENT': cls.SESSION_PERMANENT,
        }
    
    @classmethod
    def print_config_summary(cls):
        """打印配置摘要（隐藏敏感信息）"""
        print("=" * 50)
        print("账单分析系统配置摘要")
        print("=" * 50)
        print(f"MySQL: {cls.MYSQL_USER}@{cls.MYSQL_HOST}:{cls.MYSQL_PORT}/{cls.MYSQL_DATABASE}")
        print(f"Flask: {cls.HOST}:{cls.PORT} (DEBUG={cls.DEBUG})")
        print(f"SMTP: {cls.SMTP_SERVER}:{cls.SMTP_PORT} (用户: {cls.SMTP_USERNAME[:3]}***)")
        print(f"上传文件夹: {cls.UPLOAD_FOLDER}")
        print(f"最大文件大小: {cls.MAX_CONTENT_LENGTH // 1024 // 1024}MB")
        print("=" * 50)

# 创建全局配置实例
config = Config()

if __name__ == "__main__":
    # 测试配置加载
    config.print_config_summary()
    print("\nMySQL配置:")
    print(config.get_mysql_config())
    print("\nSMTP配置:")
    smtp_config = config.get_smtp_config()
    smtp_config['password'] = '***'  # 隐藏密码
    print(smtp_config)