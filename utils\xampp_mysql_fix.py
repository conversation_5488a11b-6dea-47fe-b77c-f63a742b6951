"""
XAMPP MySQL密码重置脚本
适用于XAMPP环境
"""

print("XAMPP MySQL密码重置指南")
print("=" * 40)

print("""
如果你使用的是XAMPP，请按以下步骤操作：

方法A：通过XAMPP控制面板重置
1. 打开XAMPP控制面板
2. 停止MySQL服务
3. 点击MySQL旁边的 'Config' 按钮
4. 选择 'my.ini'
5. 在 [mysqld] 部分添加：skip-grant-tables
6. 保存文件并重启MySQL
7. 运行此脚本重置密码

方法B：直接设置密码为空（简单）
1. 打开XAMPP控制面板
2. 点击MySQL旁边的 'Admin' 按钮（打开phpMyAdmin）
3. 点击 'User accounts'
4. 找到root用户，点击 'Edit privileges'
5. 点击 'Change password'
6. 选择 'No password'
7. 点击 'Go'

方法C：手动修改配置文件
在app.py中尝试以下配置：
"""

configs = [
    {
        'name': '无密码配置（XAMPP默认）',
        'config': """
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',
    'database': 'bill_analysis_db',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': True
}"""
    },
    {
        'name': '常见默认密码',
        'config': """
# 尝试这些常见密码：
# 'password': '',          # 空密码
# 'password': 'root',      # root
# 'password': '123456',    # 123456
# 'password': 'mysql',     # mysql
"""
    }
]

for i, config in enumerate(configs, 1):
    print(f"\n配置选项 {i}: {config['name']}")
    print(config['config'])

print("\n测试连接命令：")
print("python -c \"import pymysql; pymysql.connect(host='localhost', user='root', password='', database='mysql'); print('连接成功')\"")