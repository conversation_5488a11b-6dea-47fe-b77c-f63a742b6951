#!/usr/bin/env python3
"""
快速MySQL连接测试
"""

import pymysql

def test_mysql_connection():
    """测试MySQL连接"""
    print("MySQL连接测试")
    print("=" * 30)
    
    # 常见密码列表
    passwords = ['', 'root', '123456', 'mysql', 'password']
    
    for password in passwords:
        try:
            print(f"尝试密码: {'(空)' if password == '' else password}")
            
            conn = pymysql.connect(
                host='localhost',
                port=3306,
                user='root',
                password=123456,
                charset='utf8mb4'
            )
            
            print(f"✅ 连接成功！密码是: {'(空)' if password == '' else password}")
            
            # 尝试创建数据库
            cursor = conn.cursor()
            cursor.execute("CREATE DATABASE IF NOT EXISTS bill_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ 数据库 'bill_analysis_db' 创建/验证成功")
            
            # 测试访问数据库
            conn.select_db('bill_analysis_db')
            print("✅ 可以访问目标数据库")
            
            conn.close()
            
            print(f"\n请在 app.py 第39行设置密码为:")
            if password == '':
                print("'password': '',")
            else:
                print(f"'password': '{password}',")
                
            return True
            
        except pymysql.err.OperationalError as e:
            if "Access denied" in str(e):
                print("❌ 密码错误")
            elif "Can't connect" in str(e):
                print("❌ 无法连接到MySQL服务器")
                print("请确认MySQL服务已启动")
                break
            else:
                print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print("\n❌ 所有密码都失败了")
    print("\n解决建议:")
    print("1. 确认MySQL服务正在运行")
    print("2. 如果使用XAMPP，检查XAMPP控制面板中MySQL是否启动")
    print("3. 尝试重置MySQL root密码")
    print("4. 或者联系我获取进一步帮助")
    
    return False

if __name__ == "__main__":
    test_mysql_connection()