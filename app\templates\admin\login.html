<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 账单分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #e74c3c;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-info: #3498db;
            --admin-light: #ecf0f1;
            --admin-dark: #2c3e50;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus {
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border: none;
            border-radius: 12px;
            padding: 0.8rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3);
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-primary));
        }

        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .btn-login .spinner-border {
            width: 1rem;
            height: 1rem;
            margin-right: 0.5rem;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: var(--admin-accent);
            border-left: 4px solid var(--admin-accent);
        }

        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            color: var(--admin-success);
            border-left: 4px solid var(--admin-success);
        }

        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #6c757d;
        }

        .login-footer a {
            color: var(--admin-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .login-footer a:hover {
            color: var(--admin-secondary);
        }

        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }

        .forgot-password a {
            color: var(--admin-info);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: var(--admin-primary);
        }

        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                border-radius: 15px;
            }
            
            .login-header {
                padding: 1.5rem;
            }
            
            .login-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="bi bi-shield-lock"></i> 管理员登录</h1>
            <p>账单分析系统管理后台</p>
        </div>
        
        <div class="login-body">
            <!-- 错误提示 -->
            <div id="alertContainer"></div>
            
            <form id="adminLoginForm">
                <!-- 邮箱 -->
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                    <label for="email"><i class="bi bi-envelope"></i> 邮箱地址</label>
                </div>
                
                <!-- 密码 -->
                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                    <label for="password"><i class="bi bi-lock"></i> 密码</label>
                </div>
                
                <!-- 登录按钮 -->
                <button type="submit" class="btn btn-login" id="loginBtn">
                    <span class="btn-text">登录管理后台</span>
                </button>
            </form>
            
            <div class="forgot-password">
                <a href="#" onclick="showForgotPassword()">忘记密码？</a>
            </div>
        </div>
        
        <div class="login-footer">
            <p>还没有管理员账户？ <a href="/admin/register">注册管理员</a></p>
            <p><a href="/">返回首页</a></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
        });

        function initializeForm() {
            const form = document.getElementById('adminLoginForm');
            form.addEventListener('submit', handleFormSubmit);
        }

        function handleFormSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 验证表单
            if (!validateForm(data)) {
                return;
            }
            
            setLoading(true);
            hideAlerts();
            
            fetch('/admin/login_api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);
                
                if (data.success) {
                    showAlert(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect || '/admin';
                    }, 1000);
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                setLoading(false);
                showAlert('登录失败，请重试', 'danger');
                console.error('登录错误:', error);
            });
        }

        function validateForm(data) {
            if (!data.email) {
                showAlert('请输入邮箱地址', 'warning');
                return false;
            }
            
            if (!data.password) {
                showAlert('请输入密码', 'warning');
                return false;
            }
            
            return true;
        }

        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            
            if (loading) {
                loginBtn.disabled = true;
                btnText.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>登录中...';
            } else {
                loginBtn.disabled = false;
                btnText.innerHTML = '登录管理后台';
            }
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'danger' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function hideAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }

        function showForgotPassword() {
            showAlert('请联系系统管理员重置密码', 'info');
        }
    </script>
</body>
</html>
