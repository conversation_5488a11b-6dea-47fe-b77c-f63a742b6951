{% extends "admin/base.html" %}

{% block title %}用户管理{% endblock %}
{% block page_title %}用户管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h6 class="m-0"><i class="bi bi-people"></i> 用户列表</h6>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-light btn-sm" onclick="refreshUserList()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 搜索和过滤 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索用户名或邮箱...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="roleFilter">
                            <option value="">所有角色</option>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">所有状态</option>
                            <option value="active">正常</option>
                            <option value="disabled">禁用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-admin-primary w-100" onclick="searchUsers()">
                            <i class="bi bi-funnel"></i> 筛选
                        </button>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="loading-spinner"></div>
                                    <span class="ms-2">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="用户列表分页" id="paginationContainer" class="d-none">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-person-gear"></i> 编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="editUsername" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="editEmail" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editRole" class="form-label">角色</label>
                        <select class="form-select" id="editRole" required>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-admin-primary" onclick="saveUserEdit()">
                    <span id="saveUserBtnText">保存更改</span>
                    <span id="saveUserBtnSpinner" class="loading-spinner d-none"></span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentSearch = '';
    let currentRoleFilter = '';
    let currentStatusFilter = '';

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadUserList();
        initializeEventListeners();
    });

    // 初始化事件监听器
    function initializeEventListeners() {
        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });

        // 编辑用户表单提交
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveUserEdit();
        });
    }

    // 加载用户列表
    function loadUserList(page = 1) {
        currentPage = page;
        
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            search: currentSearch,
            role: currentRoleFilter,
            status: currentStatusFilter
        });

        fetch(`/admin/api/users?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderUserTable(data.data.users);
                    renderPagination(data.data.pagination);
                } else {
                    showAlert('加载用户列表失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('加载用户列表错误:', error);
                showAlert('加载用户列表失败', 'danger');
            });
    }

    // 渲染用户表格
    function renderUserTable(users) {
        const tbody = document.getElementById('usersTableBody');
        
        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">没有找到用户</td></tr>';
            return;
        }

        let html = '';
        users.forEach(user => {
            const roleClass = user.role === 'admin' ? 'badge-role-admin' : 'badge-role-user';
            const statusClass = user.status === 'active' ? 'badge-status-active' : 'badge-status-disabled';
            const statusText = user.status === 'active' ? '正常' : '禁用';
            const roleText = user.role === 'admin' ? '管理员' : '普通用户';
            
            html += `
                <tr>
                    <td>${user.id}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-person-circle text-muted me-2"></i>
                            ${user.username}
                        </div>
                    </td>
                    <td>${user.email}</td>
                    <td><span class="badge ${roleClass}">${roleText}</span></td>
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td>${formatDate(user.created_at)}</td>
                    <td>${formatDate(user.last_login)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-${user.status === 'active' ? 'warning' : 'success'}" 
                                    onclick="toggleUserStatus(${user.id}, '${user.status}', '${user.username}')" 
                                    title="${user.status === 'active' ? '禁用' : '启用'}">
                                <i class="bi bi-${user.status === 'active' ? 'ban' : 'check-circle'}"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
    }

    // 渲染分页
    function renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        const paginationEl = document.getElementById('pagination');
        
        if (pagination.pages <= 1) {
            container.classList.add('d-none');
            return;
        }
        
        container.classList.remove('d-none');
        
        let html = '';
        
        // 上一页
        if (pagination.page > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadUserList(${pagination.page - 1})">上一页</a></li>`;
        }
        
        // 页码
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.page ? 'active' : '';
            html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadUserList(${i})">${i}</a></li>`;
        }
        
        // 下一页
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadUserList(${pagination.page + 1})">下一页</a></li>`;
        }
        
        paginationEl.innerHTML = html;
    }

    // 搜索用户
    function searchUsers() {
        currentSearch = document.getElementById('searchInput').value.trim();
        currentRoleFilter = document.getElementById('roleFilter').value;
        currentStatusFilter = document.getElementById('statusFilter').value;
        loadUserList(1);
    }

    // 刷新用户列表
    function refreshUserList() {
        loadUserList(currentPage);
    }

    // 编辑用户
    function editUser(userId) {
        // 从表格中获取用户数据
        const row = event.target.closest('tr');
        const cells = row.querySelectorAll('td');
        
        document.getElementById('editUserId').value = userId;
        document.getElementById('editUsername').value = cells[1].textContent.trim();
        document.getElementById('editEmail').value = cells[2].textContent.trim();
        
        // 设置角色
        const roleText = cells[3].textContent.trim();
        document.getElementById('editRole').value = roleText === '管理员' ? 'admin' : 'user';
        
        // 显示模态框
        new bootstrap.Modal(document.getElementById('editUserModal')).show();
    }

    // 保存用户编辑
    function saveUserEdit() {
        const userId = document.getElementById('editUserId').value;
        const username = document.getElementById('editUsername').value.trim();
        const email = document.getElementById('editEmail').value.trim();
        const role = document.getElementById('editRole').value;
        
        if (!username || !email) {
            showAlert('用户名和邮箱不能为空', 'warning');
            return;
        }
        
        setSaveUserLoading(true);
        
        fetch(`/admin/api/users/${userId}/edit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                role: role
            })
        })
        .then(response => response.json())
        .then(data => {
            setSaveUserLoading(false);
            
            if (data.success) {
                showAlert(data.message, 'success');
                bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                loadUserList(currentPage);
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            setSaveUserLoading(false);
            showAlert('保存失败，请重试', 'danger');
            console.error('保存用户编辑错误:', error);
        });
    }

    // 切换用户状态
    function toggleUserStatus(userId, currentStatus, username) {
        const action = currentStatus === 'active' ? '禁用' : '启用';
        
        if (!confirm(`确定要${action}用户 "${username}" 吗？`)) {
            return;
        }
        
        fetch(`/admin/api/users/${userId}/toggle_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadUserList(currentPage);
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('操作失败，请重试', 'danger');
            console.error('切换用户状态错误:', error);
        });
    }

    // 设置保存按钮加载状态
    function setSaveUserLoading(loading) {
        const saveBtn = document.querySelector('#editUserModal .btn-admin-primary');
        const saveText = document.getElementById('saveUserBtnText');
        const saveSpinner = document.getElementById('saveUserBtnSpinner');
        
        if (loading) {
            saveBtn.disabled = true;
            saveText.classList.add('d-none');
            saveSpinner.classList.remove('d-none');
        } else {
            saveBtn.disabled = false;
            saveText.classList.remove('d-none');
            saveSpinner.classList.add('d-none');
        }
    }
</script>
{% endblock %}
