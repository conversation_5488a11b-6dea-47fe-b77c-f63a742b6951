-- ============================================================================
-- 管理员后台系统数据库迁移脚本
-- Admin Backend System Database Migration Script
-- 
-- 使用说明：
-- 1. 备份现有数据库
-- 2. 连接到bill_analysis_db数据库
-- 3. 执行此脚本：source database_migration_admin.sql;
-- ============================================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- ============================================================================
-- 1. 修改users表，添加管理员相关字段
-- ============================================================================

-- 添加role字段（用户角色）
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user' 
COMMENT '用户角色：user=普通用户，admin=管理员' AFTER is_verified;

-- 添加status字段（账户状态）
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' 
COMMENT '账户状态：active=正常，disabled=禁用' AFTER role;

-- 添加last_login字段（最后登录时间）
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL 
COMMENT '最后登录时间' AFTER status;

-- 添加索引以提高查询性能
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_role (role);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_status (status);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_last_login (last_login);

-- ============================================================================
-- 2. 创建管理员操作日志表
-- ============================================================================
CREATE TABLE IF NOT EXISTS admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID，自增主键',
    admin_id INT NOT NULL COMMENT '操作管理员ID，外键引用users.id',
    action VARCHAR(100) NOT NULL COMMENT '操作类型（如：用户管理、文件删除等）',
    target_type VARCHAR(50) COMMENT '操作目标类型（user、file、system等）',
    target_id VARCHAR(100) COMMENT '操作目标ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT '操作者IP地址',
    user_agent TEXT COMMENT '用户代理信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_target_type (target_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- ============================================================================
-- 3. 创建系统配置表
-- ============================================================================
CREATE TABLE IF NOT EXISTS system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID，自增主键',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键名',
    config_value TEXT COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否为公开配置：0=私有，1=公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ============================================================================
-- 4. 插入默认系统配置
-- ============================================================================
INSERT INTO system_configs (config_key, config_value, description, is_public) VALUES
('admin_registration_enabled', 'true', '是否允许管理员注册', 0),
('admin_registration_secret', 'admin_secret_2025', '管理员注册密钥', 0),
('system_maintenance_mode', 'false', '系统维护模式', 1),
('max_upload_size_mb', '16', '最大上传文件大小(MB)', 1),
('user_registration_enabled', 'true', '是否允许用户注册', 1)
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = CURRENT_TIMESTAMP;

-- ============================================================================
-- 5. 更新现有用户数据（如果需要）
-- ============================================================================

-- 将所有现有用户的role设置为'user'（如果字段为空）
UPDATE users SET role = 'user' WHERE role IS NULL OR role = '';

-- 将所有现有用户的status设置为'active'（如果字段为空）
UPDATE users SET status = 'active' WHERE status IS NULL OR status = '';

-- ============================================================================
-- 6. 显示更新后的表结构
-- ============================================================================
SHOW TABLES;
DESC users;
DESC admin_logs;
DESC system_configs;

-- 显示用户统计信息
SELECT 
    role,
    status,
    COUNT(*) as user_count
FROM users 
GROUP BY role, status;

-- 显示系统配置
SELECT config_key, config_value, description FROM system_configs WHERE is_public = 1;

COMMIT;
