<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员注册 - 账单分析系统</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .register-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }

        .register-header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .register-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .register-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .register-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .btn-register {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }

        .btn-register:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .verification-group {
            display: flex;
            gap: 0.5rem;
        }

        .verification-group .form-control {
            flex: 1;
        }

        .btn-send-code {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 1rem;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .btn-send-code:hover:not(:disabled) {
            background: #5a6268;
        }

        .btn-send-code:disabled {
            opacity: 0.6;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .back-link {
            text-align: center;
            margin-top: 1rem;
        }

        .back-link a {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: #dc3545;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            width: 0%;
        }

        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #198754; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card animate__animated animate__fadeInUp">
            <div class="register-header">
                <h2><i class="bi bi-shield-check"></i> 管理员注册</h2>
                <p>创建管理员账户以管理系统</p>
            </div>
            
            <div class="register-body">
                <!-- 提示信息 -->
                <div id="alertContainer"></div>
                
                <form id="adminRegisterForm">
                    <!-- 用户名 -->
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                        <label for="username"><i class="bi bi-person"></i> 用户名</label>
                    </div>
                    
                    <!-- 邮箱 -->
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                        <label for="email"><i class="bi bi-envelope"></i> 邮箱地址</label>
                    </div>
                    
                    <!-- 密码 -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password"><i class="bi bi-lock"></i> 密码</label>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <small class="text-muted" id="strengthText">请输入至少6位密码</small>
                        </div>
                    </div>
                    
                    <!-- 确认密码 -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="确认密码" required>
                        <label for="confirmPassword"><i class="bi bi-lock-fill"></i> 确认密码</label>
                    </div>
                    
                    <!-- 邮箱验证码 -->
                    <div class="form-floating">
                        <div class="verification-group">
                            <input type="text" class="form-control" id="verificationCode" name="verificationCode" placeholder="验证码" required maxlength="6">
                            <button type="button" class="btn btn-send-code" id="sendCodeBtn">发送验证码</button>
                        </div>
                        <label for="verificationCode"><i class="bi bi-shield-check"></i> 邮箱验证码</label>
                    </div>
                    
                    <!-- 管理员密钥 -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="adminSecret" name="adminSecret" placeholder="管理员密钥" required>
                        <label for="adminSecret"><i class="bi bi-key"></i> 管理员密钥</label>
                        <small class="text-muted">请联系系统管理员获取管理员注册密钥</small>
                    </div>
                    
                    <!-- 注册按钮 -->
                    <button type="submit" class="btn btn-register btn-danger" id="registerBtn">
                        <span id="registerBtnText">创建管理员账户</span>
                        <span id="registerBtnSpinner" class="loading-spinner d-none"></span>
                    </button>
                </form>
                
                <div class="back-link">
                    <a href="{{ url_for('index') }}">
                        <i class="bi bi-arrow-left"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let countdown = 0;
        let countdownInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
        });

        // 初始化表单
        function initializeForm() {
            const form = document.getElementById('adminRegisterForm');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            // 表单提交
            form.addEventListener('submit', handleFormSubmit);
            
            // 发送验证码
            sendCodeBtn.addEventListener('click', sendVerificationCode);
            
            // 密码强度检测
            passwordInput.addEventListener('input', checkPasswordStrength);
            
            // 确认密码验证
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);
        }

        // 处理表单提交
        function handleFormSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 验证表单
            if (!validateForm(data)) {
                return;
            }
            
            setLoading(true);
            hideAlerts();
            
            fetch('/admin/register_api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);
                
                if (data.success) {
                    showAlert(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                setLoading(false);
                showAlert('注册失败，请重试', 'danger');
                console.error('注册错误:', error);
            });
        }

        // 发送验证码
        function sendVerificationCode() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                showAlert('请先输入邮箱地址', 'warning');
                return;
            }
            
            if (!isValidEmail(email)) {
                showAlert('请输入有效的邮箱地址', 'warning');
                return;
            }
            
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            sendCodeBtn.disabled = true;
            sendCodeBtn.textContent = '发送中...';
            
            fetch('/send_verification_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showAlert(data.error, 'danger');
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                } else {
                    showAlert(data.message, 'success');
                    startCountdown();
                }
            })
            .catch(error => {
                showAlert('发送验证码失败，请重试', 'danger');
                sendCodeBtn.disabled = false;
                sendCodeBtn.textContent = '发送验证码';
                console.error('发送验证码错误:', error);
            });
        }

        // 开始倒计时
        function startCountdown() {
            countdown = 60;
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            
            countdownInterval = setInterval(() => {
                sendCodeBtn.textContent = `${countdown}秒后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(countdownInterval);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                }
            }, 1000);
        }

        // 检查密码强度
        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            let strength = 0;
            let text = '';
            let className = '';
            
            if (password.length >= 6) strength += 1;
            if (password.match(/[a-z]/)) strength += 1;
            if (password.match(/[A-Z]/)) strength += 1;
            if (password.match(/[0-9]/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
            
            switch (strength) {
                case 0:
                case 1:
                    text = '密码强度：弱';
                    className = 'strength-weak';
                    strengthFill.style.width = '25%';
                    break;
                case 2:
                case 3:
                    text = '密码强度：中等';
                    className = 'strength-medium';
                    strengthFill.style.width = '60%';
                    break;
                case 4:
                case 5:
                    text = '密码强度：强';
                    className = 'strength-strong';
                    strengthFill.style.width = '100%';
                    break;
            }
            
            strengthFill.className = `strength-fill ${className}`;
            strengthText.textContent = text;
        }

        // 验证密码匹配
        function validatePasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const confirmInput = document.getElementById('confirmPassword');
            
            if (confirmPassword && password !== confirmPassword) {
                confirmInput.setCustomValidity('密码不匹配');
                confirmInput.classList.add('is-invalid');
            } else {
                confirmInput.setCustomValidity('');
                confirmInput.classList.remove('is-invalid');
            }
        }

        // 验证表单
        function validateForm(data) {
            if (data.password !== data.confirmPassword) {
                showAlert('两次输入的密码不一致', 'warning');
                return false;
            }
            
            if (data.password.length < 6) {
                showAlert('密码至少需要6个字符', 'warning');
                return false;
            }
            
            if (!data.verificationCode || data.verificationCode.length !== 6) {
                showAlert('请输入6位验证码', 'warning');
                return false;
            }
            
            if (!data.adminSecret) {
                showAlert('请输入管理员密钥', 'warning');
                return false;
            }
            
            return true;
        }

        // 设置加载状态
        function setLoading(loading) {
            const registerBtn = document.getElementById('registerBtn');
            const registerBtnText = document.getElementById('registerBtnText');
            const registerBtnSpinner = document.getElementById('registerBtnSpinner');
            
            if (loading) {
                registerBtn.disabled = true;
                registerBtnText.classList.add('d-none');
                registerBtnSpinner.classList.remove('d-none');
            } else {
                registerBtn.disabled = false;
                registerBtnText.classList.remove('d-none');
                registerBtnSpinner.classList.add('d-none');
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
        }

        // 隐藏提示信息
        function hideAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }

        // 验证邮箱格式
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    </script>
</body>
</html>
