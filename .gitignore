# 环境配置文件（包含敏感信息）
.env

# Python编译文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 虚拟环境
venv/
ENV/
env/
.venv/

# IDE/编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log

# 上传文件目录
uploads/*/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 备份文件
*.bak
*.backup