#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证脚本
验证所有配置是否正确从.env文件加载
"""

from config import config
import os

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("配置验证测试")
    print("=" * 60)
    
    # 测试基本配置加载
    print("\n1. 基本配置测试:")
    print(f"   MySQL Host: {config.MYSQL_HOST}")
    print(f"   MySQL Port: {config.MYSQL_PORT}")
    print(f"   MySQL User: {config.MYSQL_USER}")
    print(f"   MySQL Database: {config.MYSQL_DATABASE}")
    print(f"   Flask Debug: {config.DEBUG}")
    print(f"   Flask Port: {config.PORT}")
    
    # 测试类型转换
    print("\n2. 类型转换测试:")
    print(f"   MYSQL_PORT type: {type(config.MYSQL_PORT)} (should be <class 'int'>)")
    print(f"   DEBUG type: {type(config.DEBUG)} (should be <class 'bool'>)")
    print(f"   ALLOWED_EXTENSIONS type: {type(config.ALLOWED_EXTENSIONS)} (should be <class 'list'>)")
    print(f"   IMAGE_EXTENSIONS type: {type(config.IMAGE_EXTENSIONS)} (should be <class 'list'>)")
    
    # 测试配置方法
    print("\n3. 配置方法测试:")
    mysql_config = config.get_mysql_config()
    smtp_config = config.get_smtp_config()
    flask_config = config.get_flask_config()
    
    print(f"   MySQL Config Keys: {list(mysql_config.keys())}")
    print(f"   SMTP Config Keys: {list(smtp_config.keys())}")
    print(f"   Flask Config Keys: {list(flask_config.keys())}")
    
    # 测试功能配置
    print("\n4. 功能配置测试:")
    print(f"   验证码长度: {config.VERIFICATION_CODE_LENGTH}")
    print(f"   验证码有效期: {config.VERIFICATION_CODE_EXPIRE_MINUTES} 分钟")
    print(f"   允许的文件扩展名: {config.ALLOWED_EXTENSIONS}")
    print(f"   图片扩展名: {config.IMAGE_EXTENSIONS}")
    print(f"   头像大小: {config.AVATAR_SIZE}x{config.AVATAR_SIZE}")
    print(f"   密码最小长度: {config.PASSWORD_MIN_LENGTH}")
    print(f"   CSV跳过行数: {config.CSV_SKIP_ROWS}")
    
    # 验证配置完整性
    print("\n5. 配置完整性检查:")
    required_configs = [
        'MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE',
        'SECRET_KEY', 'DEBUG', 'HOST', 'PORT', 'UPLOAD_FOLDER',
        'SMTP_SERVER', 'SMTP_PORT', 'SMTP_USERNAME', 'SMTP_PASSWORD',
        'VERIFICATION_CODE_LENGTH', 'VERIFICATION_CODE_EXPIRE_MINUTES',
        'ALLOWED_EXTENSIONS', 'IMAGE_EXTENSIONS', 'AVATAR_SIZE', 'PASSWORD_MIN_LENGTH'
    ]
    
    missing_configs = []
    for conf in required_configs:
        if not hasattr(config, conf):
            missing_configs.append(conf)
    
    if missing_configs:
        print(f"   ❌ 缺少配置: {missing_configs}")
    else:
        print("   ✅ 所有必需配置都已加载")
    
    # 环境变量检查
    print("\n6. 环境变量检查:")
    env_file_exists = os.path.exists('.env')
    print(f"   .env文件存在: {'✅ 是' if env_file_exists else '❌ 否'}")
    
    if env_file_exists:
        with open('.env', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        config_lines = [line for line in lines if '=' in line and not line.strip().startswith('#')]
        print(f"   .env文件配置项数量: {len(config_lines)}")
    
    print("\n" + "=" * 60)
    print("配置验证完成!")
    print("=" * 60)

def test_app_import():
    """测试应用导入"""
    print("\n测试应用导入...")
    try:
        from app import app, MYSQL_CONFIG, SMTP_CONFIG
        print("✅ 应用导入成功")
        print(f"   Flask应用配置项: {len(app.config)}")
        print(f"   MySQL配置项: {len(MYSQL_CONFIG)}")
        print(f"   SMTP配置项: {len(SMTP_CONFIG)}")
        return True
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        return False

if __name__ == "__main__":
    test_config_loading()
    test_app_import()