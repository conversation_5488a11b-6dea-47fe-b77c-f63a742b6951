from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory, session, flash
import pandas as pd
import os
import io
import chardet
from datetime import datetime
import json
import sys
import traceback
import smtplib
import random
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from werkzeug.security import generate_password_hash, check_password_hash
import pymysql
import pymysql.cursors
from config import config

# 添加调试输出
print("开始初始化应用...")

# 初始化Flask应用
app = Flask(__name__, static_folder="app/static", template_folder="app/templates")

# 配置Flask应用
flask_config = config.get_flask_config()
for key, value in flask_config.items():
    app.config[key] = value

# 获取配置
MYSQL_CONFIG = config.get_mysql_config()
SMTP_CONFIG = config.get_smtp_config()

# 打印配置摘要
config.print_config_summary()

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 数据库连接函数
def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

# 初始化数据库
def init_db():
    """初始化数据库"""
    conn = get_db_connection()
    if not conn:
        print("无法连接到数据库")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 创建用户表 - 存储用户基本信息和认证数据
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识ID，自增主键',
                username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名，唯一，不能为空',
                email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址，唯一，不能为空，用于登录',
                password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值，使用werkzeug加密',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
                is_verified TINYINT(1) DEFAULT 0 COMMENT '邮箱验证状态：0=未验证，1=已验证',
                
                INDEX idx_username (username),
                INDEX idx_email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
        ''')
        
        # 创建验证码表 - 存储邮箱验证码用于注册、找回密码等
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS verification_codes (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '验证码记录ID，自增主键',
                email VARCHAR(100) NOT NULL COMMENT '接收验证码的邮箱地址',
                code VARCHAR(10) NOT NULL COMMENT '6位数字验证码',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '验证码生成时间',
                expires_at TIMESTAMP NOT NULL COMMENT '验证码过期时间（生成后5分钟）',
                used TINYINT(1) DEFAULT 0 COMMENT '使用状态：0=未使用，1=已使用',
                
                INDEX idx_email_code (email, code),
                INDEX idx_expires_at (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码表'
        ''')
        
        # 创建账单数据表 - 存储用户上传的财务账单记录
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bill_records (
                id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账单记录ID，自增主键',
                user_id INT NOT NULL COMMENT '关联用户ID，外键引用users.id',
                record_time TIMESTAMP NOT NULL COMMENT '账单记录时间（交易发生时间）',
                category VARCHAR(100) COMMENT '交易分类（如：餐饮、交通、购物等）',
                income_expense_type VARCHAR(50) COMMENT '收支类型（收入/支出/内部收入/内部支出等）',
                amount DECIMAL(15,2) NOT NULL COMMENT '交易金额，精确到分',
                remark TEXT COMMENT '交易备注说明',
                account VARCHAR(100) COMMENT '交易账户（如：支付宝、微信等）',
                source VARCHAR(100) COMMENT '交易来源',
                tags VARCHAR(200) COMMENT '交易标签',
                extra_field TEXT COMMENT '额外字段，用于存储其他信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                
                INDEX idx_user_id (user_id),
                INDEX idx_record_time (record_time),
                INDEX idx_user_time (user_id, record_time),
                INDEX idx_category (category),
                INDEX idx_income_expense_type (income_expense_type),
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账单记录表'
        ''')
        
        # 创建上传记录表 - 记录用户文件上传历史，便于管理和统计
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS upload_records (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '上传记录ID，自增主键',
                user_id INT NOT NULL COMMENT '关联用户ID，外键引用users.id',
                filename VARCHAR(255) NOT NULL COMMENT '上传的文件名',
                file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
                record_count INT NOT NULL COMMENT '处理的账单记录条数',
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
                status VARCHAR(20) DEFAULT 'success' COMMENT '上传状态：success=成功，failed=失败',
                
                INDEX idx_user_id (user_id),
                INDEX idx_upload_time (upload_time),
                INDEX idx_status (status),
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传记录表'
        ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        print("MySQL数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        if conn:
            conn.close()
        return False

# 发送邮件函数
def send_email(to_email, subject, body):
    """发送邮件"""
    try:
        msg = MIMEMultipart()
        msg['From'] = SMTP_CONFIG['username']
        msg['To'] = to_email
        msg['Subject'] = subject
        
        msg.attach(MIMEText(body, 'html', 'utf-8'))
        
        if SMTP_CONFIG['use_ssl']:
            server = smtplib.SMTP_SSL(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        else:
            server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
            server.starttls()
            
        server.login(SMTP_CONFIG['username'], SMTP_CONFIG['password'])
        server.send_message(msg)
        server.quit()
        return True
    except Exception as e:
        print(f"发送邮件失败: {str(e)}")
        return False

# 生成验证码
def generate_verification_code():
    """生成验证码"""
    return ''.join(random.choices(string.digits, k=config.VERIFICATION_CODE_LENGTH))

# 数据库操作函数
def save_bill_data_to_db(user_id, df):
    """将账单数据保存到数据库"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
            
        cursor = conn.cursor()
        
        # 先删除该用户的旧数据
        cursor.execute('DELETE FROM bill_records WHERE user_id = %s', (user_id,))
        
        # 插入新数据
        for _, row in df.iterrows():
            try:
                # 处理时间字段
                record_time = row.get('记录时间')
                if pd.isna(record_time):
                    continue  # 跳过无效的记录
                
                # 确保时间格式正确
                if isinstance(record_time, str):
                    record_time = pd.to_datetime(record_time).strftime('%Y-%m-%d %H:%M:%S')
                elif hasattr(record_time, 'strftime'):
                    record_time = record_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    record_time = str(record_time)
                
                # 处理金额字段
                amount = row.get('金额', 0)
                if pd.isna(amount):
                    amount = 0
                else:
                    amount = float(amount)
                
                # 处理其他字段，确保None值转换为空字符串
                def safe_str(value):
                    if pd.isna(value) or value is None:
                        return ''
                    return str(value)
                
                cursor.execute('''
                    INSERT INTO bill_records 
                    (user_id, record_time, category, income_expense_type, amount, remark, account, source, tags, extra_field)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    user_id,
                    record_time,
                    safe_str(row.get('分类')),
                    safe_str(row.get('收支类型')),
                    amount,
                    safe_str(row.get('备注')),
                    safe_str(row.get('账户')),
                    safe_str(row.get('来源')),
                    safe_str(row.get('标签')),
                    safe_str(row.get('额外字段', ''))
                ))
            except Exception as row_error:
                print(f"处理数据行时出错: {row_error}, 行数据: {row.to_dict()}")
                continue  # 跳过有问题的行，继续处理下一行
        
        conn.commit()
        cursor.close()
        conn.close()
        print(f"成功保存 {len(df)} 条账单记录到数据库")
        return True
    except Exception as e:
        print(f"保存账单数据到数据库时出错: {str(e)}")
        print(traceback.format_exc())
        return False

def get_bill_data_from_db(user_id):
    """从数据库获取用户的账单数据"""
    try:
        conn = get_db_connection()
        if not conn:
            return None
            
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT record_time, category, income_expense_type, amount, remark, account, source, tags, extra_field
            FROM bill_records 
            WHERE user_id = %s 
            ORDER BY record_time DESC
        ''', (user_id,))
        
        rows = cursor.fetchall()
        cursor.close()
        conn.close()
        
        if not rows:
            return None
        
        # 转换为DataFrame格式
        columns = ['记录时间', '分类', '收支类型', '金额', '备注', '账户', '来源', '标签', '额外字段']
        data = []
        for row in rows:
            record = {}
            # MySQL返回的是字典格式（因为使用了DictCursor）
            record['记录时间'] = row['record_time'].isoformat() if row['record_time'] else ''
            record['分类'] = str(row['category']) if row['category'] else ''
            record['收支类型'] = str(row['income_expense_type']) if row['income_expense_type'] else ''
            record['金额'] = float(row['amount']) if row['amount'] is not None else 0.0
            record['备注'] = str(row['remark']) if row['remark'] else ''
            record['账户'] = str(row['account']) if row['account'] else ''
            record['来源'] = str(row['source']) if row['source'] else ''
            record['标签'] = str(row['tags']) if row['tags'] else ''
            record['额外字段'] = str(row['extra_field']) if row['extra_field'] else ''
            data.append(record)
        
        print(f"从数据库获取到 {len(data)} 条账单记录")
        return data
    except Exception as e:
        print(f"从数据库获取账单数据时出错: {str(e)}")
        print(traceback.format_exc())
        return None

def check_user_has_data(user_id):
    """检查用户是否有账单数据"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
            
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) as count FROM bill_records WHERE user_id = %s', (user_id,))
        result = cursor.fetchone()
        count = result['count'] if result else 0
        cursor.close()
        conn.close()
        
        return count > 0
    except Exception as e:
        print(f"检查用户数据时出错: {str(e)}")
        return False

# 初始化数据库
init_db()

def detect_encoding(file_path):
    """检测文件编码"""
    try:
        with open(file_path, 'rb') as f:
            result = chardet.detect(f.read())
        encoding = result.get('encoding', 'utf-8')
        if not encoding:
            encoding = 'utf-8'
        return encoding
    except Exception as e:
        print(f"检测文件编码时出错: {e}")
        return 'utf-8'  # 默认返回utf-8编码

def process_csv_file(file_path):
    """处理CSV文件，跳过前10行，从第11行开始读取数据"""
    try:
        print(f"开始处理CSV文件: {file_path}")
        print(f"文件是否存在: {os.path.exists(file_path)}")
        print(f"文件大小: {os.path.getsize(file_path)} 字节")
        
        # 尝试读取前20行查看文件内容
        with open(file_path, 'rb') as f:
            raw_content = f.read(1024)  # 读取前1KB内容
        print(f"文件前1KB内容(hex): {raw_content.hex()[:100]}...")
        
        encoding = detect_encoding(file_path)
        print(f"检测到文件编码: {encoding}")
        
        # 尝试先读取前几行查看内容
        try:
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                sample_lines = [next(f).strip() for _ in range(15) if f]
            print(f"文件前15行内容示例: {sample_lines}")
        except Exception as e:
            print(f"读取文件前几行时出错: {e}")
        
        # 尝试使用低级方法读取文件
        try:
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                lines = [line.strip() for line in f if line.strip()]
            
            # 跳过前N行
            data_lines = lines[config.CSV_SKIP_ROWS:]
            if not data_lines:
                raise ValueError("文件内容为空或格式不正确")
            
            # 假设第一行是列名
            header_line = data_lines[0]
            header = header_line.split(',')
            print(f"提取的列名: {header}")
            
            # 处理数据行
            data = []
            for line in data_lines[1:]:
                values = line.split(',')
                if len(values) >= len(header):
                    data.append(values[:len(header)])
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=header)
            print(f"使用低级方法读取成功，数据形状: {df.shape}")
        except Exception as e:
            print(f"使用低级方法读取出错: {e}")
            # 尝试使用pandas直接读取
            try:
                df = pd.read_csv(file_path, encoding=encoding, skiprows=config.CSV_SKIP_ROWS, on_bad_lines='skip')
                print(f"使用pandas读取CSV成功，数据形状: {df.shape}")
            except Exception as e2:
                print(f"使用pandas读取CSV也出错: {e2}")
                raise ValueError(f"无法读取CSV文件: {str(e2)}")
        
        # 打印DataFrame的前几行数据，查看实际内容
        print(f"DataFrame前5行数据:\n{df.head()}")
        print(f"CSV列名: {df.columns.tolist()}")
        
        # 列名映射
        column_count = len(df.columns)
        print(f"实际列数: {column_count}")
        
        # 根据列数定义适当的列名
        if column_count == 9:  # 处理9列的情况
            new_columns = ['记录时间', '分类', '收支类型', '金额', '备注', '账户', '来源', '标签', '额外字段']
            df.columns = new_columns
            print(f"使用9列的新列名: {new_columns}")
        else:  # 处理其他列数的情况
            expected_columns = ['记录时间', '分类', '收支类型', '金额', '备注', '账户', '来源', '标签']
            
            # 根据实际列数自动调整预期列名
            if column_count > len(expected_columns):
                # 如果实际列数更多，增加额外的列名
                for i in range(len(expected_columns), column_count):
                    expected_columns.append(f"额外字段{i-len(expected_columns)+1}")
            elif column_count < len(expected_columns):
                # 如果实际列数较少，截取预期列名
                expected_columns = expected_columns[:column_count]
            
            # 设置列名
            df.columns = expected_columns
            print(f"调整后的列名: {df.columns.tolist()}")
        
        # 处理日期格式
        try:
            df['记录时间'] = pd.to_datetime(df['记录时间'], errors='coerce')
            print("日期格式处理完成")
        except Exception as e:
            print(f"处理日期格式时出错: {e}")
        
        # 确保金额为数值类型
        try:
            df['金额'] = pd.to_numeric(df['金额'], errors='coerce')
            print("金额格式处理完成")
        except Exception as e:
            print(f"处理金额格式时出错: {e}")
        
        # 对数据进行处理，删除没有记录时间或金额的行
        df = df.dropna(subset=['记录时间', '金额'])
        print(f"删除无效行后的数据形状: {df.shape}")
        
        print(f"CSV处理成功，最终数据形状: {df.shape}")
        return df
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        print(traceback.format_exc())
        return None

@app.route('/')
def index():
    """首页"""
    print("访问首页")
    # 如果用户已登录且有数据，可以提供直接跳转的选项
    if 'user_id' in session:
        has_data = check_user_has_data(session['user_id'])
        return render_template('index.html', 
                             logged_in=True, 
                             username=session.get('username'),
                             has_data=has_data)
    return render_template('index.html', logged_in=False)

@app.route('/api/user_status')
def get_user_status():
    """获取用户状态信息"""
    if 'user_id' not in session:
        return jsonify({'logged_in': False})
    
    try:
        has_data = check_user_has_data(session['user_id'])
        return jsonify({
            'logged_in': True,
            'username': session.get('username'),
            'email': session.get('email'),
            'has_data': has_data
        })
    except Exception as e:
        print(f"获取用户状态错误: {str(e)}")
        return jsonify({'logged_in': False})

@app.route('/api/test_db')
def test_db():
    """测试数据库连接"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        # 检查bill_records表是否存在
        cursor.execute("SELECT COUNT(*) FROM bill_records")
        record_count = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': '数据库连接正常',
            'tables': [table[0] for table in tables],
            'bill_records_count': record_count
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'数据库连接失败: {str(e)}',
            'error': str(e)
        }), 500

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        return render_template('register.html')
    
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        verification_code = data.get('verification_code')
        
        if not all([username, email, password, verification_code]):
            return jsonify({'error': '所有字段都是必填的'}), 400
        
        # 验证验证码
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT code FROM verification_codes 
            WHERE email = %s AND used = 0 AND expires_at > NOW()
            ORDER BY created_at DESC LIMIT 1
        ''', (email,))
        
        result = cursor.fetchone()
        if not result or result['code'] != verification_code:
            conn.close()
            return jsonify({'error': '验证码无效或已过期'}), 400
        
        # 检查用户名和邮箱是否已存在
        cursor.execute('SELECT id FROM users WHERE username = %s OR email = %s', (username, email))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': '用户名或邮箱已存在'}), 400
        
        # 创建用户
        password_hash = generate_password_hash(password)
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, is_verified)
            VALUES (%s, %s, %s, 1)
        ''', (username, email, password_hash))
        
        # 标记验证码为已使用
        cursor.execute('''
            UPDATE verification_codes SET used = 1 
            WHERE email = %s AND code = %s
        ''', (email, verification_code))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': '注册成功'}), 200
        
    except Exception as e:
        print(f"注册错误: {str(e)}")
        return jsonify({'error': '注册失败，请重试'}), 500

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        return render_template('login.html')
    
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        if not all([email, password]):
            return jsonify({'error': '邮箱和密码都是必填的'}), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, password_hash FROM users 
            WHERE email = %s AND is_verified = 1
        ''', (email,))
        
        user = cursor.fetchone()
        conn.close()
        
        if not user or not check_password_hash(user['password_hash'], password):
            return jsonify({'error': '邮箱或密码错误'}), 400
        
        # 设置session
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['email'] = email
        
        return jsonify({'message': '登录成功', 'redirect': url_for('index')}), 200
        
    except Exception as e:
        print(f"登录错误: {str(e)}")
        return jsonify({'error': '登录失败，请重试'}), 500

@app.route('/logout')
def logout():
    """用户退出"""
    session.clear()
    return redirect(url_for('index'))

@app.route('/send_verification_code', methods=['POST'])
def send_verification_code():
    """发送邮箱验证码"""
    try:
        data = request.get_json()
        email = data.get('email')
        
        if not email:
            return jsonify({'error': '邮箱地址是必填的'}), 400
        
        # 生成验证码
        code = generate_verification_code()
        
        # 保存到数据库
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 设置验证码过期时间
        cursor.execute(f'''
            INSERT INTO verification_codes (email, code, expires_at)
            VALUES (%s, %s, DATE_ADD(NOW(), INTERVAL {config.VERIFICATION_CODE_EXPIRE_MINUTES} MINUTE))
        ''', (email, code))
        
        conn.commit()
        conn.close()
        
        # 发送邮件
        subject = "账单分析系统 - 邮箱验证码"
        body = f"""
        <html>
        <body>
            <h2>账单分析系统邮箱验证</h2>
            <p>您的验证码是：<strong style="font-size: 24px; color: #007bff;">{code}</strong></p>
            <p>验证码有效期为{config.VERIFICATION_CODE_EXPIRE_MINUTES}分钟，请及时使用。</p>
            <p>如果您没有申请验证码，请忽略此邮件。</p>
        </body>
        </html>
        """
        
        if send_email(email, subject, body):
            return jsonify({'message': '验证码已发送到您的邮箱'}), 200
        else:
            return jsonify({'error': '发送验证码失败，请重试'}), 500
            
    except Exception as e:
        print(f"发送验证码错误: {str(e)}")
        return jsonify({'error': '发送验证码失败，请重试'}), 500

@app.route('/test-file-io', methods=['GET'])
def test_file_io():
    """测试文件IO操作"""
    try:
        # 测试目录
        upload_dir = app.config['UPLOAD_FOLDER']
        test_file_path = os.path.join(upload_dir, 'test.txt')
        test_content = "这是一个测试文件\n" + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 创建目录
        os.makedirs(upload_dir, exist_ok=True)
        dir_status = f"目录创建状态: {os.path.exists(upload_dir)}"
        
        # 写入文件
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        write_status = f"文件写入状态: {os.path.exists(test_file_path)}"
        
        # 读取文件
        with open(test_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        read_status = f"文件读取状态: {'成功' if content == test_content else '失败'}"
        
        return jsonify({
            'status': 'success',
            'message': '文件IO测试成功',
            'details': {
                'dir_path': upload_dir,
                'file_path': test_file_path,
                'dir_status': dir_status,
                'write_status': write_status,
                'read_status': read_status,
                'content': content
            }
        })
    except Exception as e:
        error_msg = f"文件IO测试失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': error_msg,
            'traceback': traceback.format_exc()
        }), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'error': '请先登录后再上传文件', 'need_login': True}), 401
    
    try:
        print("开始处理文件上传请求")
        if 'file' not in request.files:
            print("错误：没有文件上传")
            return jsonify({'error': '没有文件上传'}), 400
        
        file = request.files['file']
        print(f"收到文件: {file.filename}")
        
        if file.filename == '':
            print("错误：未选择文件")
            return jsonify({'error': '未选择文件'}), 400
        
        file_extension = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
        if file and file_extension in config.ALLOWED_EXTENSIONS:
            # 确保上传目录存在
            user_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(session['user_id']))
            os.makedirs(user_upload_dir, exist_ok=True)
            print(f"用户上传目录: {user_upload_dir}")
            
            # 使用安全的文件名
            from werkzeug.utils import secure_filename
            safe_filename = secure_filename(file.filename)
            file_path = os.path.join(user_upload_dir, safe_filename)
            print(f"保存文件到: {file_path}")
            
            try:
                # 将上传的文件先保存到内存中
                file_content = file.read()
                print(f"读取文件内容，大小: {len(file_content)} 字节")
                
                # 将内容写入文件
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                print(f"文件保存成功: {file_path}")
                print(f"文件是否存在: {os.path.exists(file_path)}")
                print(f"文件大小: {os.path.getsize(file_path)} 字节")
            except Exception as e:
                print(f"保存文件出错: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'error': f'保存文件失败: {str(e)}'}), 500
            
            # 处理CSV文件
            df = process_csv_file(file_path)
            if df is None:
                print("文件处理失败")
                return jsonify({'error': '文件处理失败，请检查文件格式是否正确'}), 500
            
            try:
                # 将处理后的数据保存到数据库
                print(f"开始保存数据到数据库，用户ID: {session['user_id']}")
                if save_bill_data_to_db(session['user_id'], df):
                    print("数据保存到数据库成功")
                    
                    # 保存上传记录
                    record_count = len(df)
                    file_size = len(file_content)
                    if save_upload_record(session['user_id'], safe_filename, file_size, record_count):
                        print("上传记录保存成功")
                    else:
                        print("上传记录保存失败，但不影响主流程")
                    
                    # 可选：删除临时文件
                    try:
                        os.remove(file_path)
                        print("临时文件删除成功")
                    except Exception as e:
                        print(f"删除临时文件失败: {str(e)}")
                    
                    return jsonify({
                        'message': '文件上传并处理成功',
                        'redirect': url_for('analyze')
                    })
                else:
                    print("数据保存到数据库失败")
                    return jsonify({'error': '数据保存失败，请重试'}), 500
                    
            except Exception as e:
                print(f"处理数据时出错: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'error': f'处理数据失败: {str(e)}'}), 500
            
        else:
            print(f"不支持的文件类型: {file.filename}")
            allowed_ext_str = ', '.join(config.ALLOWED_EXTENSIONS).upper()
            return jsonify({'error': f'仅支持{allowed_ext_str}文件'}), 400
    except Exception as e:
        print(f"处理上传请求时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'服务器处理错误: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """访问上传的文件"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/analyze')
def analyze():
    """分析页面"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    # 检查用户是否有数据
    if not check_user_has_data(session['user_id']):
        return redirect(url_for('index'))
    
    try:
        # 从数据库读取数据
        data = get_bill_data_from_db(session['user_id'])
        if data is None:
            return render_template('error.html', error='无法获取账单数据')
        
        return render_template('analyze.html', data=data, username=session.get('username'))
    except Exception as e:
        print(f"分析页面加载时出错: {str(e)}")
        print(traceback.format_exc())
        return render_template('error.html', error=str(e))

@app.route('/api/data')
def get_data():
    """提供API接口获取处理后的数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = get_bill_data_from_db(session['user_id'])
        if data is None:
            return jsonify({'error': '没有数据可用'}), 404
        return jsonify(data)
    except Exception as e:
        print(f"获取数据API出错: {str(e)}")
        return jsonify({'error': '获取数据失败'}), 500

@app.route('/api/summary')
def get_summary():
    """获取账单汇总数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = get_bill_data_from_db(session['user_id'])
        if data is None:
            return jsonify({'error': '没有数据可用'}), 404
        
        df = pd.DataFrame(data)
        
        # 确保数据类型正确
        df['记录时间'] = pd.to_datetime(df['记录时间'], errors='coerce')
        df['金额'] = pd.to_numeric(df['金额'], errors='coerce').fillna(0)
        
        # 删除无效记录
        df = df.dropna(subset=['记录时间'])
        
        if len(df) == 0:
            return jsonify({
                'total_income': 0.0,
                'total_expense': 0.0,
                'balance': 0.0,
                'category_summary': {},
                'type_summary': {},
                'daily_trend': {},
                'monthly_summary': {},
                'account_summary': {},
                'weekly_summary': [],
                'cumulative_trend': {},
                'hourly_summary': {}
            })
        
        # 收入和支出总计
        income = df[df['收支类型'].isin(['收入', '内部收入', '资金收入', '投资收入'])]['金额'].sum()
        expense = df[df['收支类型'].isin(['支出', '内部支出', '资金支出', '投资支出'])]['金额'].sum()
        
        # 按分类汇总
        category_summary = df.groupby('分类')['金额'].sum().to_dict()
        category_summary = {k: float(v) for k, v in category_summary.items() if not pd.isna(k)}
        
        # 按收支类型汇总
        type_summary = df.groupby('收支类型')['金额'].sum().to_dict()
        type_summary = {k: float(v) for k, v in type_summary.items() if not pd.isna(k)}
        
        # 按天统计趋势
        df['日期'] = df['记录时间'].dt.date
        daily_trend = df.groupby('日期')['金额'].sum().to_dict()
        daily_trend = {str(k): float(v) for k, v in daily_trend.items()}

        # 按月统计收支
        df['年月'] = df['记录时间'].dt.to_period('M').astype(str)
        monthly_data = df.groupby(['年月', '收支类型'])['金额'].sum().unstack(fill_value=0)
        monthly_summary = {}
        
        if not monthly_data.empty:
            for month in monthly_data.index:
                income_cols = [col for col in monthly_data.columns if '收入' in str(col)]
                expense_cols = [col for col in monthly_data.columns if '支出' in str(col)]
                
                monthly_summary[month] = {
                    'income': float(monthly_data.loc[month, income_cols].sum() if income_cols else 0),
                    'expense': float(monthly_data.loc[month, expense_cols].sum() if expense_cols else 0)
                }

        # 按账户统计
        account_summary = df.groupby('账户')['金额'].sum().to_dict()
        account_summary = {k: float(v) for k, v in account_summary.items() if k and str(k) != 'nan' and not pd.isna(k)}

        # 周收支热力图数据
        df['星期'] = df['记录时间'].dt.dayofweek
        df['小时'] = df['记录时间'].dt.hour
        weekly_heatmap = df.groupby(['小时', '星期']).size().reset_index(name='count')
        weekly_summary = [[int(row['小时']), int(row['星期']), int(row['count'])] for _, row in weekly_heatmap.iterrows()]

        # 收支累计趋势
        df_sorted = df.sort_values('记录时间')
        df_sorted['累计金额'] = df_sorted['金额'].cumsum()
        cumulative_trend = df_sorted.groupby('日期')['累计金额'].last().to_dict()
        cumulative_trend = {str(k): float(v) for k, v in cumulative_trend.items()}

        # 时段分析
        hourly_summary = df.groupby('小时').size().to_dict()
        hourly_summary = {str(k): int(v) for k, v in hourly_summary.items()}

        summary = {
            'total_income': float(income),
            'total_expense': float(expense),
            'balance': float(income - expense),
            'category_summary': category_summary,
            'type_summary': type_summary,
            'daily_trend': daily_trend,
            'monthly_summary': monthly_summary,
            'account_summary': account_summary,
            'weekly_summary': weekly_summary,
            'cumulative_trend': cumulative_trend,
            'hourly_summary': hourly_summary
        }
        
        return jsonify(summary)
    except Exception as e:
        print(f"生成汇总数据时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'生成汇总数据失败: {str(e)}'}), 500

@app.route('/api/ranking')
def get_ranking():
    """获取账单排行榜数据"""
    # 检查用户是否已登录
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        print("排行榜API: 开始加载数据...")
        data = get_bill_data_from_db(session['user_id'])
        if data is None:
            print("排行榜API: 无法找到数据")
            return jsonify({'error': '没有数据可用'}), 404
        
        df = pd.DataFrame(data)
        print(f"排行榜API: 成功加载数据，形状: {df.shape}")
        
        # 检查必要的列是否存在
        required_columns = ['备注', '收支类型', '金额']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"排行榜API: 缺少必要的列: {missing_columns}, 可用列: {df.columns.tolist()}")
            return jsonify({'error': f'数据格式不正确，缺少必要的列: {missing_columns}'}), 500
        
        # 处理备注为空的情况
        df['备注'].fillna('其他', inplace=True)
        print("排行榜API: 填充空备注为'其他'")
        
        # 提取备注中的主要内容（去除订单号等信息）
        def extract_main_content(remark):
            if not isinstance(remark, str):
                return '其他'
            # 尝试提取主要内容（例如：从"分账-闲鱼币智能软件服务费(123456)扣款"中提取"闲鱼币智能软件服务费"）
            import re
            patterns = [
                r'^分账-(.+?)\(', # 匹配"分账-XXX("格式
                r'^(.+?)-',      # 匹配"XXX-"格式
                r'^(.+?)（',      # 匹配"XXX（"格式
                r'^(.+?)\(',     # 匹配"XXX("格式
                r'^(.+?)扣款',    # 匹配"XXX扣款"格式
                r'^(.+?)$',      # 直接使用全部内容
            ]
            
            for pattern in patterns:
                match = re.search(pattern, remark)
                if match:
                    content = match.group(1).strip()
                    # 如果提取内容太长，截取前20个字符
                    return content[:20] if len(content) > 20 else content
            
            return '其他'
        
        # 添加处理后的备注列
        print("排行榜API: 提取备注主要内容")
        df['处理后备注'] = df['备注'].apply(extract_main_content)
        
        # 确保金额为数值类型
        print("排行榜API: 转换金额为数值类型")
        df['金额'] = pd.to_numeric(df['金额'], errors='coerce').fillna(0)
        
        # 检查收支类型
        print(f"排行榜API: 收支类型值: {df['收支类型'].unique().tolist()}")
        
        # 按收支类型和处理后的备注分组计算金额总和
        print("排行榜API: 按收支类型和处理后备注分组汇总")
        grouped_data = df.groupby(['收支类型', '处理后备注'])['金额'].sum().reset_index()
        print(f"排行榜API: 分组后数据形状: {grouped_data.shape}")
        
        # 分别获取收入和支出的排行榜
        income_types = ['收入', '内部收入', '资金收入', '投资收入']
        expense_types = ['支出', '内部支出', '资金支出', '投资支出']
        
        # 使用isin函数前先确保所有值都是字符串
        grouped_data['收支类型'] = grouped_data['收支类型'].astype(str)
        
        # 收入数据
        income_data = grouped_data[grouped_data['收支类型'].isin(income_types)]
        print(f"排行榜API: 收入数据项数: {len(income_data)}")
        
        # 支出数据
        expense_data = grouped_data[grouped_data['收支类型'].isin(expense_types)]
        print(f"排行榜API: 支出数据项数: {len(expense_data)}")
        
        # 如果没有明确的收入或支出类别，尝试根据金额正负来区分
        if len(income_data) == 0 and len(expense_data) == 0:
            print("排行榜API: 未找到明确的收支类型，尝试根据金额正负区分")
            income_data = grouped_data[grouped_data['金额'] > 0]
            expense_data = grouped_data[grouped_data['金额'] < 0]
            print(f"排行榜API: 重新分类后 - 收入数据项数: {len(income_data)}, 支出数据项数: {len(expense_data)}")
        
        # 排序并获取所有数据（不限制数量）
        all_income = income_data.sort_values('金额', ascending=False).to_dict('records')
        all_expense = expense_data.sort_values('金额', ascending=False).to_dict('records')
        
        # 如果支出金额为负数，转换为正数方便前端展示
        for item in all_expense:
            if item['金额'] < 0:
                item['金额'] = abs(item['金额'])

        result = {
            'income': all_income,
            'expense': all_expense
        }

        print(f"排行榜API: 返回结果 - 收入项数: {len(all_income)}, 支出项数: {len(all_expense)}")
        return jsonify(result)
    except Exception as e:
        print(f"生成排行榜数据时出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'生成排行榜数据失败: {str(e)}'}), 500

# 添加全局错误处理
@app.errorhandler(500)
def internal_server_error(e):
    return jsonify({
        'error': '服务器内部错误',
        'message': str(e)
    }), 500

@app.errorhandler(404)
def page_not_found(e):
    return jsonify({
        'error': '页面不存在',
        'message': str(e)
    }), 404

# 创建错误页面模板
@app.route('/create-error-template')
def create_error_template():
    error_template_path = os.path.join(app.template_folder, 'error.html')
    if not os.path.exists(error_template_path):
        with open(error_template_path, 'w', encoding='utf-8') as f:
            f.write('''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - 账单分析系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", sans-serif;
            padding-top: 50px;
        }
        .error-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 6px 10px rgba(0,0,0,0.08);
            text-align: center;
        }
        .error-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1 class="mb-4">发生错误</h1>
            <p class="text-muted mb-4">{{ error }}</p>
            <a href="/" class="btn btn-primary">返回首页</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>''')
    return jsonify({'message': '错误页面模板创建成功'})

@app.route('/upload_avatar', methods=['POST'])
def upload_avatar():
    """上传用户头像"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        if 'avatar' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        file = request.files['avatar']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        # 检查文件类型
        allowed_mime_types = [f'image/{ext}' for ext in config.IMAGE_EXTENSIONS]
        if file.content_type not in allowed_mime_types:
            ext_str = ', '.join(config.IMAGE_EXTENSIONS).upper()
            return jsonify({'error': f'仅支持{ext_str}格式的图片'}), 400
        
        # 创建头像目录
        avatar_dir = os.path.join(app.static_folder, 'uploads', 'avatars')
        os.makedirs(avatar_dir, exist_ok=True)
        
        # 保存文件
        filename = f"{session['user_id']}.jpg"
        file_path = os.path.join(avatar_dir, filename)
        
        # 如果是其他格式的图片，需要转换为JPG
        from PIL import Image
        image = Image.open(file.stream)
        # 转换为RGB模式（去除透明度）
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # 调整图片大小
        avatar_size = (config.AVATAR_SIZE, config.AVATAR_SIZE)
        image = image.resize(avatar_size, Image.Resampling.LANCZOS)
        image.save(file_path, 'JPEG', quality=85)
        
        return jsonify({
            'message': '头像上传成功', 
            'user_id': session['user_id']
        }), 200
        
    except ImportError:
        return jsonify({'error': '系统缺少图片处理库，请联系管理员'}), 500
    except Exception as e:
        print(f"上传头像错误: {str(e)}")
        return jsonify({'error': '上传失败，请重试'}), 500

@app.route('/change_password', methods=['POST'])
def change_password():
    """修改密码（通过邮箱验证码验证）"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        new_password = data.get('new_password')
        verification_code = data.get('verification_code')
        
        if not all([new_password, verification_code]):
            return jsonify({'error': '所有字段都是必填的'}), 400
        
        if len(new_password) < config.PASSWORD_MIN_LENGTH:
            return jsonify({'error': f'新密码长度至少为{config.PASSWORD_MIN_LENGTH}位'}), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 获取用户邮箱
        cursor.execute('SELECT email FROM users WHERE id = %s', (session['user_id'],))
        user = cursor.fetchone()
        
        if not user:
            conn.close()
            return jsonify({'error': '用户不存在'}), 404
        
        user_email = user['email']
        
        # 验证邮箱验证码
        cursor.execute('''
            SELECT code FROM verification_codes 
            WHERE email = %s AND used = 0 AND expires_at > NOW()
            ORDER BY created_at DESC LIMIT 1
        ''', (user_email,))
        
        result = cursor.fetchone()
        if not result or result['code'] != verification_code:
            conn.close()
            return jsonify({'error': '验证码无效或已过期'}), 400
        
        # 更新密码
        new_password_hash = generate_password_hash(new_password)
        cursor.execute('UPDATE users SET password_hash = %s WHERE id = %s', 
                      (new_password_hash, session['user_id']))
        
        # 标记验证码为已使用
        cursor.execute('UPDATE verification_codes SET used = 1 WHERE email = %s AND code = %s', 
                      (user_email, verification_code))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': '密码修改成功'}), 200
        
    except Exception as e:
        print(f"修改密码错误: {str(e)}")
        return jsonify({'error': '修改失败，请重试'}), 500

@app.route('/change_email', methods=['POST'])
def change_email():
    """修改邮箱"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        new_email = data.get('new_email')
        verification_code = data.get('verification_code')
        
        if not all([new_email, verification_code]):
            return jsonify({'error': '所有字段都是必填的'}), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 验证验证码
        cursor.execute('''
            SELECT code FROM verification_codes 
            WHERE email = %s AND used = 0 AND expires_at > NOW()
            ORDER BY created_at DESC LIMIT 1
        ''', (new_email,))
        
        result = cursor.fetchone()
        if not result or result['code'] != verification_code:
            conn.close()
            return jsonify({'error': '验证码无效或已过期'}), 400
        
        # 检查新邮箱是否已被使用
        cursor.execute('SELECT id FROM users WHERE email = %s AND id != %s', (new_email, session['user_id']))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': '该邮箱已被其他用户使用'}), 400
        
        # 更新邮箱
        cursor.execute('UPDATE users SET email = %s WHERE id = %s', (new_email, session['user_id']))
        
        # 标记验证码为已使用
        cursor.execute('UPDATE verification_codes SET used = 1 WHERE email = %s AND code = %s', 
                      (new_email, verification_code))
        
        conn.commit()
        conn.close()
        
        # 更新session
        session['email'] = new_email
        
        return jsonify({'message': '邮箱修改成功'}), 200
        
    except Exception as e:
        print(f"修改邮箱错误: {str(e)}")
        return jsonify({'error': '修改失败，请重试'}), 500

@app.route('/export_user_data', methods=['GET'])
def export_user_data():
    """导出用户数据"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = get_bill_data_from_db(session['user_id'])
        if data is None:
            return jsonify({'error': '没有数据可导出'}), 404
        
        df = pd.DataFrame(data)
        
        # 创建临时目录和CSV文件
        user_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(session['user_id']))
        os.makedirs(user_upload_dir, exist_ok=True)
        
        csv_filename = f"账单数据_{session['username']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        csv_path = os.path.join(user_upload_dir, csv_filename)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        return send_from_directory(user_upload_dir, csv_filename, as_attachment=True)
        
    except Exception as e:
        print(f"导出数据错误: {str(e)}")
        return jsonify({'error': '导出失败，请重试'}), 500

@app.route('/clear_user_data', methods=['POST'])
def clear_user_data():
    """清空用户数据"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 删除用户的账单数据
        cursor.execute('DELETE FROM bill_records WHERE user_id = %s', (session['user_id'],))
        
        conn.commit()
        conn.close()
        
        # 可选：同时删除用户上传目录中的文件
        user_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(session['user_id']))
        if os.path.exists(user_upload_dir):
            import shutil
            shutil.rmtree(user_upload_dir)
            os.makedirs(user_upload_dir, exist_ok=True)
        
        return jsonify({'message': '数据清空成功'}), 200
        
    except Exception as e:
        print(f"清空数据错误: {str(e)}")
        return jsonify({'error': '操作失败，请重试'}), 500

@app.route('/delete_account', methods=['POST'])
def delete_account():
    """注销账户"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        user_id = session['user_id']
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 先删除用户的账单数据（外键约束会自动处理，但为了确保明确删除）
        cursor.execute('DELETE FROM bill_records WHERE user_id = %s', (user_id,))
        
        # 删除用户相关的验证码
        cursor.execute('''
            DELETE FROM verification_codes 
            WHERE email IN (SELECT email FROM users WHERE id = %s)
        ''', (user_id,))
        
        # 删除用户记录
        cursor.execute('DELETE FROM users WHERE id = %s', (user_id,))
        
        conn.commit()
        conn.close()
        
        # 删除用户文件
        user_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], str(user_id))
        if os.path.exists(user_upload_dir):
            import shutil
            shutil.rmtree(user_upload_dir)
        
        # 删除头像
        avatar_path = os.path.join(app.static_folder, 'uploads', 'avatars', f'{user_id}.jpg')
        if os.path.exists(avatar_path):
            os.remove(avatar_path)
        
        # 清除session
        session.clear()
        
        return jsonify({'message': '账户注销成功'}), 200
        
    except Exception as e:
        print(f"注销账户错误: {str(e)}")
        return jsonify({'error': '操作失败，请重试'}), 500

@app.route('/api/user_info', methods=['GET'])
def get_user_info():
    """获取用户信息"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT username, email, created_at FROM users 
            WHERE id = %s
        ''', (session['user_id'],))
        
        user = cursor.fetchone()
        conn.close()
        
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        return jsonify({
            'username': user['username'],
            'email': user['email'],
            'created_at': user['created_at']
        }), 200
        
    except Exception as e:
        print(f"获取用户信息错误: {str(e)}")
        return jsonify({'error': '获取用户信息失败，请重试'}), 500

@app.route('/change_username', methods=['POST'])
def change_username():
    """修改用户名"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        data = request.get_json()
        new_username = data.get('new_username')
        
        if not new_username:
            return jsonify({'error': '用户名不能为空'}), 400
            
        if len(new_username.strip()) < 2:
            return jsonify({'error': '用户名至少需要2个字符'}), 400
        
        new_username = new_username.strip()
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 检查用户名是否已被使用
        cursor.execute('SELECT id FROM users WHERE username = %s AND id != %s', (new_username, session['user_id']))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': '该用户名已被使用'}), 400
        
        # 更新用户名
        cursor.execute('UPDATE users SET username = %s WHERE id = %s', (new_username, session['user_id']))
        
        conn.commit()
        conn.close()
        
        # 更新session
        session['username'] = new_username
        
        return jsonify({'message': '用户名修改成功', 'new_username': new_username}), 200
        
    except Exception as e:
        print(f"修改用户名错误: {str(e)}")
        return jsonify({'error': '修改失败，请重试'}), 500

@app.route('/avatar/<user_id>')
def get_avatar(user_id):
    """获取用户头像，如果不存在则返回默认头像"""
    try:
        avatar_dir = os.path.join(app.static_folder, 'uploads', 'avatars')
        user_avatar_path = os.path.join(avatar_dir, f'{user_id}.jpg')
        default_avatar_path = os.path.join(avatar_dir, 'default.jpg')
        
        # 如果用户头像存在，返回用户头像
        if os.path.exists(user_avatar_path):
            return send_from_directory(avatar_dir, f'{user_id}.jpg')
        
        # 否则返回默认头像
        if os.path.exists(default_avatar_path):
            return send_from_directory(avatar_dir, 'default.jpg')
        
        # 如果默认头像也不存在，创建一个简单的默认头像
        from PIL import Image, ImageDraw
        
        # 创建默认头像
        avatar_size = (config.AVATAR_SIZE, config.AVATAR_SIZE)
        img = Image.new('RGB', avatar_size, '#E5E7EB')
        draw = ImageDraw.Draw(img)
        
        # 绘制头部圆形
        center_x = config.AVATAR_SIZE // 2
        head_center = (center_x, config.AVATAR_SIZE * 2 // 5)
        head_radius = config.AVATAR_SIZE // 6
        draw.ellipse([head_center[0]-head_radius, head_center[1]-head_radius, 
                      head_center[0]+head_radius, head_center[1]+head_radius], 
                     fill='#9CA3AF')
        
        # 绘制身体部分  
        body_width = config.AVATAR_SIZE // 3
        body_start_x = center_x - body_width // 2  
        body_end_x = center_x + body_width // 2
        body_start_y = config.AVATAR_SIZE * 4 // 5
        body_points = [(body_start_x, body_start_y), (body_end_x, body_start_y), 
                       (body_end_x, config.AVATAR_SIZE), (body_start_x, config.AVATAR_SIZE)]
        draw.polygon(body_points, fill='#9CA3AF')
        
        # 保存默认头像
        img.save(default_avatar_path, 'JPEG', quality=90)
        
        return send_from_directory(avatar_dir, 'default.jpg')
        
    except Exception as e:
        print(f"获取头像错误: {str(e)}")
        # 返回一个简单的1x1像素透明图片作为占位符
        from flask import Response
        return Response(
            b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82',
            mimetype='image/png'
        )

@app.route('/api/upload_records', methods=['GET'])
def get_upload_records():
    """获取用户上传记录"""
    print(f"获取上传记录请求 - session: {session}")
    if 'user_id' not in session:
        print("用户未登录")
        return jsonify({'error': '请先登录'}), 401
    
    try:
        user_id = session['user_id']
        print(f"用户ID: {user_id}")
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, file_size, record_count, upload_time, status
            FROM upload_records 
            WHERE user_id = %s 
            ORDER BY upload_time DESC
        ''', (user_id,))
        
        records = cursor.fetchall()
        print(f"查询到 {len(records)} 条记录")
        conn.close()
        
        # 格式化数据
        upload_records = []
        for record in records:
            upload_records.append({
                'id': record['id'],
                'filename': record['filename'],
                'file_size': record['file_size'],
                'record_count': record['record_count'],
                'upload_time': record['upload_time'],
                'status': record['status']
            })
        
        print(f"返回数据: {upload_records}")
        return jsonify(upload_records)
    except Exception as e:
        print(f"获取上传记录错误: {str(e)}")
        return jsonify({'error': '获取上传记录失败，请重试'}), 500

@app.route('/api/delete_upload_record/<int:record_id>', methods=['DELETE'])
def delete_upload_record(record_id):
    """删除上传记录及其对应的账单数据"""
    if 'user_id' not in session:
        return jsonify({'error': '请先登录'}), 401
    
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        # 验证记录是否属于当前用户
        cursor.execute('SELECT id FROM upload_records WHERE id = %s AND user_id = %s', (record_id, session['user_id']))
        record = cursor.fetchone()
        
        if not record:
            conn.close()
            return jsonify({'error': '记录不存在或无权删除'}), 404
        
        # 删除该记录对应的所有账单数据
        # 注意：这里我们删除用户的所有账单数据，因为当前系统每次上传都会替换全部数据
        # 如果需要更精确的删除，可以在upload_records表中添加关联字段
        cursor.execute('DELETE FROM bill_records WHERE user_id = %s', (session['user_id'],))
        
        # 删除上传记录
        cursor.execute('DELETE FROM upload_records WHERE id = %s', (record_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': '删除成功'}), 200
        
    except Exception as e:
        print(f"删除上传记录错误: {str(e)}")
        return jsonify({'error': '删除失败，请重试'}), 500

def save_upload_record(user_id, filename, file_size, record_count):
    """保存上传记录"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO upload_records (user_id, filename, file_size, record_count, status)
            VALUES (%s, %s, %s, %s, 'success')
        ''', (user_id, filename, file_size, record_count))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"保存上传记录错误: {str(e)}")
        return False

print("开始启动Flask应用...")

if __name__ == '__main__':
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"模板目录: {os.path.join(os.getcwd(), 'app/templates')}")
    print(f"模板目录是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates'))}")
    print(f"index.html是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates/index.html'))}")
    print(f"analyze.html是否存在: {os.path.exists(os.path.join(os.getcwd(), 'app/templates/analyze.html'))}")
    print(f"上传目录: {app.config['UPLOAD_FOLDER']}")
    print(f"上传目录是否存在: {os.path.exists(app.config['UPLOAD_FOLDER'])}")
    app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG) 