{% extends "admin/base.html" %}

{% block title %}仪表板{% endblock %}
{% block page_title %}系统仪表板{% endblock %}

{% block content %}
<div class="row">
    <!-- 统计卡片 -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number" id="totalUsers">-</div>
                    <div class="stats-label">总用户数</div>
                </div>
                <div class="col-auto">
                    <i class="bi bi-people stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number" id="activeUsers">-</div>
                    <div class="stats-label">活跃用户</div>
                </div>
                <div class="col-auto">
                    <i class="bi bi-person-check stats-icon text-success"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number" id="totalRecords">-</div>
                    <div class="stats-label">账单记录</div>
                </div>
                <div class="col-auto">
                    <i class="bi bi-receipt stats-icon text-info"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-number" id="totalUploads">-</div>
                    <div class="stats-label">文件上传</div>
                </div>
                <div class="col-auto">
                    <i class="bi bi-cloud-upload stats-icon text-warning"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 用户注册趋势图 -->
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h6 class="m-0"><i class="bi bi-graph-up"></i> 用户注册趋势</h6>
            </div>
            <div class="card-body">
                <div id="userTrendChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h6 class="m-0"><i class="bi bi-clock-history"></i> 最近活动</h6>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="text-center text-muted">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 系统状态 -->
    <div class="col-xl-6 col-lg-6 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h6 class="m-0"><i class="bi bi-server"></i> 系统状态</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success" id="dbStatus">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <small class="text-muted">数据库连接</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info" id="systemUptime">
                                <i class="bi bi-clock"></i>
                            </div>
                            <small class="text-muted">系统运行时间</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <small class="text-muted">存储使用情况</small>
                        <div class="progress mt-1" style="height: 8px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 0%" id="storageProgress"></div>
                        </div>
                        <small class="text-muted" id="storageInfo">计算中...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-xl-6 col-lg-6 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h6 class="m-0"><i class="bi bi-lightning"></i> 快速操作</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_users') }}" class="btn btn-admin-primary">
                        <i class="bi bi-people"></i> 管理用户
                    </a>
                    <button class="btn btn-outline-primary" onclick="showComingSoon('文件清理')">
                        <i class="bi bi-trash"></i> 清理文件
                    </button>
                    <button class="btn btn-outline-info" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新数据
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showComingSoon('系统备份')">
                        <i class="bi bi-download"></i> 系统备份
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let userTrendChart = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadDashboardData();
        initUserTrendChart();
    });

    // 加载仪表板数据
    function loadDashboardData() {
        fetch('/admin/api/dashboard_stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatsCards(data.data);
                    updateRecentActivities(data.data.recent_activities || []);
                    updateSystemStatus(data.data.system_status || {});
                    updateUserTrendChart(data.data.user_trend || []);
                } else {
                    showAlert('加载仪表板数据失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('加载仪表板数据错误:', error);
                showAlert('加载仪表板数据失败', 'danger');
            });
    }

    // 更新统计卡片
    function updateStatsCards(data) {
        document.getElementById('totalUsers').textContent = data.total_users || 0;
        document.getElementById('activeUsers').textContent = data.active_users || 0;
        document.getElementById('totalRecords').textContent = data.total_records || 0;
        document.getElementById('totalUploads').textContent = data.total_uploads || 0;
    }

    // 更新最近活动
    function updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        
        if (activities.length === 0) {
            container.innerHTML = '<div class="text-center text-muted"><p>暂无最近活动</p></div>';
            return;
        }

        let html = '';
        activities.forEach(activity => {
            html += `
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="bi bi-${getActivityIcon(activity.action)} text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold">${activity.description}</div>
                        <small class="text-muted">${formatDate(activity.created_at)}</small>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    // 获取活动图标
    function getActivityIcon(action) {
        const icons = {
            'user_register': 'person-plus',
            'file_upload': 'cloud-upload',
            'admin_login': 'shield-check',
            'user_login': 'box-arrow-in-right',
            'default': 'activity'
        };
        return icons[action] || icons.default;
    }

    // 更新系统状态
    function updateSystemStatus(status) {
        // 数据库状态
        const dbStatus = document.getElementById('dbStatus');
        if (status.database_connected) {
            dbStatus.innerHTML = '<i class="bi bi-check-circle"></i>';
            dbStatus.className = 'h4 text-success';
        } else {
            dbStatus.innerHTML = '<i class="bi bi-x-circle"></i>';
            dbStatus.className = 'h4 text-danger';
        }

        // 存储使用情况
        if (status.storage_usage) {
            const progress = document.getElementById('storageProgress');
            const info = document.getElementById('storageInfo');
            const percentage = (status.storage_usage.used / status.storage_usage.total * 100).toFixed(1);
            
            progress.style.width = percentage + '%';
            info.textContent = `${formatFileSize(status.storage_usage.used)} / ${formatFileSize(status.storage_usage.total)} (${percentage}%)`;
        }
    }

    // 初始化用户趋势图表
    function initUserTrendChart() {
        userTrendChart = echarts.init(document.getElementById('userTrendChart'));
        
        const option = {
            title: {
                text: '最近30天用户注册趋势',
                textStyle: {
                    fontSize: 14,
                    color: '#666'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '新增用户',
                type: 'line',
                smooth: true,
                data: [],
                itemStyle: {
                    color: '#0d6efd'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(13, 110, 253, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(13, 110, 253, 0.1)'
                        }]
                    }
                }
            }]
        };
        
        userTrendChart.setOption(option);
    }

    // 更新用户趋势图表
    function updateUserTrendChart(trendData) {
        if (!userTrendChart || !trendData.length) return;
        
        const dates = trendData.map(item => item.date);
        const counts = trendData.map(item => item.count);
        
        userTrendChart.setOption({
            xAxis: {
                data: dates
            },
            series: [{
                data: counts
            }]
        });
    }

    // 刷新仪表板
    function refreshDashboard() {
        showAlert('正在刷新数据...', 'info');
        loadDashboardData();
    }

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        if (userTrendChart) {
            userTrendChart.resize();
        }
    });
</script>
{% endblock %}
