<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理员后台{% endblock %} - 账单分析系统</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
    
    <style>
        :root {
            --admin-primary: #0d6efd;
            --admin-secondary: #6c757d;
            --admin-success: #198754;
            --admin-danger: #dc3545;
            --admin-warning: #ffc107;
            --admin-info: #0dcaf0;
            --admin-sidebar-bg: #212529;
            --admin-sidebar-width: 250px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--admin-sidebar-width);
            background: var(--admin-sidebar-bg);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .admin-sidebar .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid #495057;
        }

        .admin-sidebar .sidebar-header h4 {
            color: white;
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .admin-sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: var(--admin-primary);
        }

        .admin-sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 1.2rem;
        }

        .admin-main {
            margin-left: var(--admin-sidebar-width);
            min-height: 100vh;
        }

        .admin-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-content {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--admin-primary);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .stats-card .stats-icon {
            font-size: 2.5rem;
            color: var(--admin-primary);
        }

        .stats-card .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }

        .stats-card .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .admin-card .card-header {
            background: linear-gradient(135deg, var(--admin-primary), #0056b3);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
            padding: 1rem 1.5rem;
        }

        .admin-card .card-body {
            padding: 1.5rem;
        }

        .btn-admin-primary {
            background: var(--admin-primary);
            border-color: var(--admin-primary);
            color: white;
        }

        .btn-admin-primary:hover {
            background: #0056b3;
            border-color: #0056b3;
            color: white;
        }

        .table-admin {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-admin thead th {
            background: var(--admin-primary);
            color: white;
            border: none;
            font-weight: 600;
        }

        .badge-role-admin {
            background: var(--admin-danger);
        }

        .badge-role-user {
            background: var(--admin-info);
        }

        .badge-status-active {
            background: var(--admin-success);
        }

        .badge-status-disabled {
            background: var(--admin-secondary);
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-main {
                margin-left: 0;
            }

            .admin-header {
                padding: 1rem;
            }

            .admin-content {
                padding: 1rem;
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--admin-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert-admin {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="admin-sidebar">
        <div class="sidebar-header">
            <h4><i class="bi bi-shield-check"></i> 管理员后台</h4>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin_dashboard' %}active{% endif %}" href="{{ url_for('admin_dashboard') }}">
                    <i class="bi bi-speedometer2"></i>仪表板
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin_users' %}active{% endif %}" href="{{ url_for('admin_users') }}">
                    <i class="bi bi-people"></i>用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showComingSoon('文件管理')">
                    <i class="bi bi-folder"></i>文件管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showComingSoon('系统监控')">
                    <i class="bi bi-activity"></i>系统监控
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showComingSoon('操作日志')">
                    <i class="bi bi-journal-text"></i>操作日志
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showComingSoon('系统设置')">
                    <i class="bi bi-gear"></i>系统设置
                </a>
            </li>
            <li class="nav-item mt-3">
                <hr class="text-light">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-house"></i>返回前台
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="bi bi-box-arrow-right"></i>退出登录
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <div class="admin-main">
        <!-- 顶部导航 -->
        <header class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-outline-secondary d-md-none" id="sidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h5 class="mb-0 ms-2 d-inline">{% block page_title %}管理员后台{% endblock %}</h5>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3">
                        <i class="bi bi-person-circle"></i>
                        {{ username }}
                    </span>
                    <span class="badge bg-danger">管理员</span>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <main class="admin-content">
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.querySelector('.admin-sidebar').classList.toggle('show');
        });

        // 通用提示函数
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-admin" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 在页面顶部显示提示
            const container = document.querySelector('.admin-content');
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // 3秒后自动消失
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }

        // 即将推出提示
        function showComingSoon(feature) {
            showAlert(`${feature}功能即将推出，敬请期待！`, 'info');
        }

        // 确认对话框
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
