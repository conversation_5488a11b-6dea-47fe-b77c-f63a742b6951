-- ============================================================================
-- 账单分析系统MySQL数据库初始化脚本
-- Database Initialization Script for Bill Analysis System (MySQL)
-- 
-- 使用说明：
-- 1. 创建数据库：CREATE DATABASE bill_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- 2. 使用数据库：USE bill_analysis_db;
-- 3. 执行此脚本：source init_mysql_database.sql;
-- ============================================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- ============================================================================
-- 1. 用户表 (users) - 存储用户基本信息和认证数据
-- ============================================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识ID，自增主键',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名，唯一，不能为空',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址，唯一，不能为空，用于登录',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值，使用werkzeug加密',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
    is_verified TINYINT(1) DEFAULT 0 COMMENT '邮箱验证状态：0=未验证，1=已验证',
    
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ============================================================================
-- 2. 邮箱验证码表 (verification_codes) - 存储邮箱验证码用于注册、找回密码等
-- ============================================================================
CREATE TABLE IF NOT EXISTS verification_codes (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '验证码记录ID，自增主键',
    email VARCHAR(100) NOT NULL COMMENT '接收验证码的邮箱地址',
    code VARCHAR(10) NOT NULL COMMENT '6位数字验证码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '验证码生成时间',
    expires_at TIMESTAMP NOT NULL COMMENT '验证码过期时间（生成后5分钟）',
    used TINYINT(1) DEFAULT 0 COMMENT '使用状态：0=未使用，1=已使用',
    
    INDEX idx_email_code (email, code),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码表';

-- ============================================================================
-- 3. 账单记录表 (bill_records) - 存储用户上传的财务账单记录
-- ============================================================================
CREATE TABLE IF NOT EXISTS bill_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账单记录ID，自增主键',
    user_id INT NOT NULL COMMENT '关联用户ID，外键引用users.id',
    record_time TIMESTAMP NOT NULL COMMENT '账单记录时间（交易发生时间）',
    category VARCHAR(100) COMMENT '交易分类（如：餐饮、交通、购物等）',
    income_expense_type VARCHAR(50) COMMENT '收支类型（收入/支出/内部收入/内部支出等）',
    amount DECIMAL(15,2) NOT NULL COMMENT '交易金额，精确到分',
    remark TEXT COMMENT '交易备注说明',
    account VARCHAR(100) COMMENT '交易账户（如：支付宝、微信等）',
    source VARCHAR(100) COMMENT '交易来源',
    tags VARCHAR(200) COMMENT '交易标签',
    extra_field TEXT COMMENT '额外字段，用于存储其他信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_record_time (record_time),
    INDEX idx_user_time (user_id, record_time),
    INDEX idx_category (category),
    INDEX idx_income_expense_type (income_expense_type),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账单记录表';

-- ============================================================================
-- 4. 上传记录表 (upload_records) - 记录用户文件上传历史，便于管理和统计
-- ============================================================================
CREATE TABLE IF NOT EXISTS upload_records (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '上传记录ID，自增主键',
    user_id INT NOT NULL COMMENT '关联用户ID，外键引用users.id',
    filename VARCHAR(255) NOT NULL COMMENT '上传的文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    record_count INT NOT NULL COMMENT '处理的账单记录条数',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status VARCHAR(20) DEFAULT 'success' COMMENT '上传状态：success=成功，failed=失败',
    
    INDEX idx_user_id (user_id),
    INDEX idx_upload_time (upload_time),
    INDEX idx_status (status),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传记录表';

-- ============================================================================
-- 5. 插入初始数据（可选）
-- ============================================================================

-- 可以在这里插入一些初始数据，比如管理员账户等
-- INSERT INTO users (username, email, password_hash, is_verified) VALUES 
-- ('admin', '<EMAIL>', 'your_hashed_password_here', 1);

-- ============================================================================
-- 6. 显示表结构信息
-- ============================================================================
SHOW TABLES;
DESC users;
DESC verification_codes;
DESC bill_records;
DESC upload_records;