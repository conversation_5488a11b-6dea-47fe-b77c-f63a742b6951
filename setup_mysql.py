"""
MySQL密码设置脚本
用于为MySQL root用户设置密码
"""

import pymysql
import getpass

def set_mysql_password():
    """设置MySQL密码"""
    print("MySQL密码设置向导")
    print("=" * 30)
    
    # 首先尝试无密码连接（全新安装的MySQL可能允许）
    try:
        print("正在尝试连接MySQL...")
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        print("✅ 成功连接到MySQL（无密码）")
        
        # 设置新密码
        new_password = getpass.getpass("请输入新的root密码: ")
        cursor = conn.cursor()
        
        # 设置密码
        cursor.execute(f"ALTER USER 'root'@'localhost' IDENTIFIED BY '{new_password}';")
        cursor.execute("FLUSH PRIVILEGES;")
        
        print("✅ 密码设置成功！")
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS bill_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        print("✅ 数据库 'bill_analysis_db' 创建成功！")
        
        conn.close()
        
        print(f"\n请在app.py中将密码配置修改为:")
        print(f"'password': '{new_password}',")
        
        return new_password
        
    except pymysql.err.OperationalError as e:
        print(f"❌ 连接失败: {e}")
        print("\n可能的解决方案:")
        print("1. MySQL服务未启动")
        print("2. root用户已有密码")
        print("3. 需要重置MySQL密码")
        return None

def test_connection(password):
    """测试数据库连接"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password=password,
            database='bill_analysis_db',
            charset='utf8mb4'
        )
        print("✅ 数据库连接测试成功！")
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    password = set_mysql_password()
    if password:
        test_connection(password)