#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限验证装饰器模块
Authentication and Authorization Decorators
"""

from functools import wraps
from flask import session, jsonify, redirect, url_for, request
import pymysql
from config import config
from datetime import datetime

def get_db_connection():
    """获取数据库连接"""
    try:
        mysql_config = config.get_mysql_config()
        connection = pymysql.connect(**mysql_config)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def get_user_info(user_id):
    """获取用户信息"""
    try:
        conn = get_db_connection()
        if not conn:
            return None
            
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, username, email, role, status, last_login 
            FROM users WHERE id = %s
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        return user
    except Exception as e:
        print(f"获取用户信息失败: {str(e)}")
        return None

def is_admin(user_id):
    """检查用户是否为管理员"""
    user = get_user_info(user_id)
    return user and user['role'] == 'admin' and user['status'] == 'active'

def is_user_active(user_id):
    """检查用户是否为活跃状态"""
    user = get_user_info(user_id)
    return user and user['status'] == 'active'

def update_last_login(user_id):
    """更新用户最后登录时间"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
            
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE users SET last_login = NOW() WHERE id = %s
        ''', (user_id,))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"更新最后登录时间失败: {str(e)}")
        return False

def log_admin_action(admin_id, action, target_type=None, target_id=None, description=None):
    """记录管理员操作日志"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
            
        cursor = conn.cursor()
        
        # 获取IP地址和用户代理
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
        user_agent = request.environ.get('HTTP_USER_AGENT', '')
        
        cursor.execute('''
            INSERT INTO admin_logs (admin_id, action, target_type, target_id, description, ip_address, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        ''', (admin_id, action, target_type, target_id, description, ip_address, user_agent))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"记录管理员操作日志失败: {str(e)}")
        return False

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # 如果是API请求，返回JSON错误
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': '请先登录', 'need_login': True}), 401
            # 否则重定向到首页
            return redirect(url_for('index'))
        
        # 检查用户是否仍然活跃
        if not is_user_active(session['user_id']):
            session.clear()
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': '账户已被禁用', 'need_login': True}), 401
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 首先检查是否登录
        if 'user_id' not in session:
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': '请先登录', 'need_login': True}), 401
            return redirect(url_for('index'))
        
        # 检查是否为管理员
        if not is_admin(session['user_id']):
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': '权限不足，需要管理员权限', 'need_admin': True}), 403
            return redirect(url_for('index'))
        
        # 更新最后登录时间
        update_last_login(session['user_id'])
        
        return f(*args, **kwargs)
    return decorated_function

def api_response(success=True, message='', data=None, error_code=None):
    """统一API响应格式"""
    response = {
        'success': success,
        'message': message,
        'timestamp': datetime.now().isoformat()
    }
    
    if data is not None:
        response['data'] = data
    
    if error_code is not None:
        response['error_code'] = error_code
    
    return response

def validate_admin_secret(secret):
    """验证管理员注册密钥"""
    return secret == config.ADMIN_REGISTRATION_SECRET

def get_system_config(key, default=None):
    """获取系统配置"""
    try:
        conn = get_db_connection()
        if not conn:
            return default
            
        cursor = conn.cursor()
        cursor.execute('''
            SELECT config_value FROM system_configs WHERE config_key = %s
        ''', (key,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            value = result['config_value']
            # 尝试转换布尔值
            if value.lower() in ('true', 'false'):
                return value.lower() == 'true'
            # 尝试转换数字
            try:
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            except ValueError:
                return value
        
        return default
    except Exception as e:
        print(f"获取系统配置失败: {str(e)}")
        return default

def set_system_config(key, value, description=None):
    """设置系统配置"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
            
        cursor = conn.cursor()
        
        # 转换值为字符串
        if isinstance(value, bool):
            value = 'true' if value else 'false'
        else:
            value = str(value)
        
        cursor.execute('''
            INSERT INTO system_configs (config_key, config_value, description)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            config_value = VALUES(config_value),
            description = COALESCE(VALUES(description), description),
            updated_at = CURRENT_TIMESTAMP
        ''', (key, value, description))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"设置系统配置失败: {str(e)}")
        return False
