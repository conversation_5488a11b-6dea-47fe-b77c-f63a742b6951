#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def test_mysql_connection():
    """测试MySQL连接"""
    print("MySQL连接测试")
    print("=" * 30)
    
    # 常见密码列表
    passwords = ['', 'root', '123456', 'mysql', 'password']
    
    for password in passwords:
        try:
            print(f"尝试密码: {password if password else '(空)'}")
            
            conn = pymysql.connect(
                host='localhost',
                port=3306,
                user='root',
                password=password,
                charset='utf8mb4'
            )
            
            print(f"成功！密码是: {password if password else '(空)'}")
            
            # 尝试创建数据库
            cursor = conn.cursor()
            cursor.execute("CREATE DATABASE IF NOT EXISTS bill_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("数据库创建成功")
            
            conn.close()
            
            print(f"\n请在 app.py 第39行设置:")
            print(f"'password': '{password}',")
                
            return password
            
        except pymysql.err.OperationalError as e:
            if "Access denied" in str(e):
                print("密码错误")
            elif "Can't connect" in str(e):
                print("无法连接到MySQL服务器")
                print("请确认MySQL服务已启动")
                return None
            else:
                print(f"连接错误: {e}")
        except Exception as e:
            print(f"其他错误: {e}")
    
    print("所有密码都失败了")
    print("请检查MySQL服务是否启动")
    return None

if __name__ == "__main__":
    result = test_mysql_connection()
    if result is not None:
        print(f"找到正确密码: {result}")
    else:
        print("未找到正确密码")